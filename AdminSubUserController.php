<?php

    namespace App\Admin\Controllers;

    use Admin;
    use App\Admin\BatchAction\BatchSetSubUserTplBatchAction;
    use App\Admin\Forms\admin\BatchSetSubUserTplForm;
    use App\Admin\Forms\admin\subUser;
    use App\Admin\Repositories\AdminSubUser;
    use App\Admin\Repositories\WwUserAddRecord;
    use App\Models\AdminUser;
    use App\Models\WwTpl;
    use App\Models\WwUsersGroup;
    use Dcat\Admin\Form;
    use Dcat\Admin\Grid;
    use Dcat\Admin\Http\Controllers\AdminController;
    use Dcat\Admin\Show;
    use Dcat\Admin\Widgets\Modal;
    use Illuminate\Support\Facades\Hash;

    class AdminSubUserController extends AdminController
    {
        const AddRecordColumn = [
            'corp_name'=>'企微',
            'ww_user_name'=>'销售名称',
            'ww_user_id'=>'销售ID'
        ];
        /**
         * Remove the specified resource from storage.
         *
         * @param int $id
         */
        public function destroy($id)
        {
            //先将adminUser账户内的信息删除
            /** @var \App\Models\AdminSubUser $adminSubUser */
            $adminSubUser = \App\Models\AdminSubUser::query()->with("adminInfo")->find($id);
            if (!AdminUser::isAdmin($adminSubUser)) {
                return $this->form()->response()->error("无权限操作");
            }
            if ($adminSubUser && $adminSubUser->adminInfo) {
                $adminSubUser->adminInfo->status = 0;
                $adminSubUser->adminInfo->save();
            }
            return $this->form()->destroy($id);
        }

        /**
         * Make a form builder.
         *
         * @return Form
         */
        protected function form()
        {
            return Form::make(new AdminSubUser(['roles']), function (Form $form) {
                $form->hidden("admin_uid")->default(Admin::user()->id);
//                $form->password('password','密码')->minLength(6);
                if (!$form->isCreating() && !AdminUser::isAdmin($form->model())) {
                    $form->display('N')->value("无数据");
                } else {
                    $form->display("数据共享说明")->value("主账户数据，共享给子账户展示与使用")->help("如落地页模板，可以直接展示给子账户，子账户在创建投放链接的时候，可以直接选择，销售客服分组同理");
                    $tpl_list = WwTpl::query()->where('admin_uid', Admin::user()->id)->get()->toArray();
                    $data = [
                        [
                            'id' => -1,
                            'parent_id' => 0,
                            'name' => '全部',
                            'order' => 0
                        ]
                    ];
                    foreach ($tpl_list as $k => $v) {
                        if($v['type'] == 1){
                            $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #21b978">投</i>';
                        }else{
                            $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #edc30e">审</i>';
                        }
                        $data[] = [
                            'id' => $v['id'],
                            'parent_id' => -1,
                            'name' => $dot.$v['name'],
                            'order' => $v['id']
                        ];
                    }
                    $form->tree('tpl_ids', '落地页模板')
                        ->nodes($data) // 设置所有节点
                        ->expand(true)
                        ->treeState(false)
                        ->customFormat(function ($v) { // 格式化外部注入的值
                            return $v;
                        })->help("选择全部，即代表全部的落地页模板，无需再单独选择");

                    $wwUserGroupData = WwUsersGroup::query()->where('admin_uid', Admin::user()->id)->get()->toArray();
                    $wwUserGroupOption = [
                        [
                            'id' => -1,
                            'parent_id' => 0,
                            'name' => '全部',
                            'order' => 0
                        ]
                    ];
                    foreach ($wwUserGroupData as $k => $v) {
                        if($v['type'] == 1){
                            $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #21b978">投</i>';
                        }else{
                            $dot = '<i class="fa fa-circle" style="font-size: 13px;color: #edc30e">审</i>';
                        }
                        $wwUserGroupOption[] = [
                            'id' => $v['id'],
                            'parent_id' => -1,
                            'name' => $dot.$v['title'],
                            'order' => $v['id']
                        ];
                    }
                    $form->tree('ww_user_group_ids', '销售客服分组')
                        ->nodes($wwUserGroupOption) // 设置所有节点
                        ->expand(true)
                        ->treeState(false)
                        ->customFormat(function ($v) { // 格式化外部注入的值
                            return $v;
                        })->help("选择全部，即代表全部的销售客服分组，无需再单独选择");

                    $form->multipleSelect("hide_add_record_column","进粉记录隐藏列")->options(self::AddRecordColumn);


                    $form->divider();
                    $form->display("权限配置说明")->value("以下是配置子账户的权限，即用户可以看到的菜单")->help("不涉及数据共享，数据请通过上面的数据共享配置，如未配置共享，子账户仅能查看自身账户的数据，如销售客服分组，子账号仅能看到自己创建的」");
                    $rolesOption = [];
                    $rolesData = config('admin.database.roles_model')::query()->whereIn("id",[3,4,5,6,7])->pluck('name', 'id');
                    foreach ($rolesData as $k => $v) {
                        $rolesOption[] = [
                            'id' => $k,
                            'parent_id' => 0,
                            'name' => self::getRoleDesc($k),
                            'order' => $k
                        ];
                    }
                    $form->tree('roles', '权限')
                        ->nodes($rolesOption)
                        ->customFormat(function ($v) {
                            return array_column($v, 'id');
                        });
                }
                $form->disableViewButton();

                // 忽略 roles 字段的自动保存
                $form->ignore('roles');

            })->saved(function (Form $form) {
                // 在保存完成后处理角色逻辑
                $this->handleUserRoles($form);
            });

        }

        /**
         * 处理用户角色分配逻辑
         */
        private function handleUserRoles(Form $form)
        {
            // 获取表单提交的角色数据
            $formRoles = request()->input('roles');

            if (empty($formRoles)) {
                return;
            }

            // 处理角色数据格式
            if (is_string($formRoles)) {
                $chooseRoles = array_map('intval', explode(',', $formRoles));
            } else {
                $chooseRoles = array_map('intval', (array)$formRoles);
            }

            // 直接操作模型，获取当前用户
            $userId = $form->model()->id;
            $adminSubUser = \App\Models\AdminSubUser::with('roles')->find($userId);

            if (!$adminSubUser) {
                return;
            }

            // 获取当前所有角色
            $currentRoles = $adminSubUser->roles()->pluck('role_id')->toArray();

            // 可管理角色范围
            $manageableRoleIs = [3, 4, 5, 6, 7];

            // 找出超管分配的角色（不在可管理范围内的）
            $superAdminRoleIs = array_diff($currentRoles, $manageableRoleIs);

            // 合并角色：用户选择的 + 超管分配的
            $finalRoles = array_unique(array_merge($chooseRoles, $superAdminRoleIs));

            // 直接同步角色到数据库
            $adminSubUser->roles()->sync($finalRoles);
        }

        public static function getRoleDesc($roleId): string
        {
            $data = [
                3 => '完整权限「包含以下全部（除数据统计-整体，数据统计-整体需单独配置）」',
                4 => '基础权限「包含腾讯广告账户管理、落地页模板、企微投放链接、进粉记录、账号管理功能」',
                5 => '企微与销售管理「包含企微管理、销售客服管理、销售客服分组」',
                6 => '数据统计「自身账号的数据统计，包含按销售统计、按组统计、按企业微信统计」',
                7 => '数据统计-整体「主账户下所有账户数据合计，按照企业微信主体统计，如某企业微信今日合计进粉量」',
            ];
            return $data[$roleId];
        }

        protected function detail($id)
        {
            return Show::make($id, new AdminSubUser(), function (Show $show) {
                if (!AdminUser::isAdmin($show->model())) {
                    $show->field('N')->value("无数据");
                } else {
                    $show->field('id');
                }
            });
        }

        /**
         * Make a grid builder.
         *
         * @return Grid
         */
        protected function grid()
        {
            return Grid::make(new AdminSubUser(['adminInfo','roles']), function (Grid $grid) {
                $grid->paginate(AdminUser::getPaginate());
                $modal = Modal::make()
                    ->lg()
                    ->title('创建子账号')
                    ->body(subUser::make())
                    ->button('<button class="btn btn-primary float-right" style="margin-left: 5px"><i class="feather icon-plus-square"></i>&nbsp&nbsp创建子账号</button>');
                $grid->tools($modal);
                $grid->model()->whereIn("admin_uid", \App\Models\AdminSubUser::getAdminUids(Admin::user()->id))->orderByDesc("id");
                $grid->column('id')->sortable();
                $grid->column('adminInfo.username', "账号");
                $grid->column('roles','权限')->display(function($value){
                    $data = $value->pluck("name");
                    return implode("<br/>", json_decode($data));
                });
                $grid->column('tpl_ids','共享落地页')->display(function($value){
                    $ids = json_decode($value,true);
                    if(!$ids){
                        return '无';
                    }
                    if(in_array(-1,$ids)){
                        return '全部';
                    }
                    $tplList = WwTpl::query()->whereIn("id",$ids)->pluck("name");
                    return implode("<br/>", $tplList->toArray());
                });
                $grid->column('ww_user_group_ids','共享销售组')->display(function($value){
                    $ids = json_decode($value,true);
                    if(!$ids){
                        return '无';
                    }
                    if(in_array(-1,$ids)){
                        return '全部';
                    }
                    $wwUserGroup = WwUsersGroup::query()->whereIn("id",$ids)->pluck("title");
                    return implode("<br/>", $wwUserGroup->toArray());
                });
//                $grid->column('hide_add_record_column','进粉记录隐藏列')->display(function($value){
//                    $ids = json_decode($value,true);
//                    if(!$ids){
//                        return '无';
//                    }
//                    $return = [];
//                    foreach($ids as $id){
//                        $return[] = self::AddRecordColumn[$id];
//                    }
//                    return implode("<br/>", $return);
//                });
                $grid->column('created_at');
                $grid->column('updated_at')->sortable();
                $grid->disableCreateButton();
                $grid->disableViewButton();
                $grid->filter(function (Grid\Filter $filter) {
                    $filter->expand();
                    $filter->panel();
                    $filter->equal('id')->width(2);
                    $filter->like('adminInfo.username',"账号")->width(2);
                });
            });
        }
    }
